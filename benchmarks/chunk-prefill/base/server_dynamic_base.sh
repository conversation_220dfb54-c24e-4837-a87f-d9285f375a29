#!/bin/bash

MODEL="qwen"
TOKENIZER="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
IGNORE_EOS="--ignore-eos"
DATASET_NAME="random"
PORT=8361
LOGFILE="${MODEL}_dynamic_base.log"

# 文件锁，确保同一时间只有一个请求访问服务
LOCKFILE="/tmp/qwen3-30b_bench-dynamic_base.lock"

acquire_lock() {
  exec 200>"$LOCKFILE"
  flock -n 200 \
    && return 0 \
    || { echo "已有其他进程在运行，退出..."; exit 1; }
}

release_lock() {
  flock -u 200
  rm -f "$LOCKFILE"
}

# 定义输入输出长度和并发数的组合，确保一一对应
# 输入长度范围：0.8~1k, 1.6~2k, 3.0~3.6k, 16~20k
# 输出长度范围：0.3~0.5k
# 这里的输入输出长度均为token数，按范围中间值或上下界取整
COMBINATIONS=(
  "48 900 400"    # 0.8~1k, 0.3~0.5k
  "33 1800 400"   # 1.6~2k, 0.3~0.5k
  "27 3300 400"   # 3.0~3.6k, 0.3~0.5k
  "8 18000 400"   # 16~20k, 0.3~0.5k
)

# 1. 先启动服务
echo "启动Qwen3-30B-A3B服务..."
HIP_VISIBLE_DEVICES=6,7 VLLM_USE_V1=1 PYTHONPATH=/home/<USER>/ VLLM_USE_FLASH_ATTN_PA=1 \
  python -m vllm.entrypoints.openai.api_server \
  --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ \
  -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port $PORT --disable-log-requests --no-enable-prefix-caching --no-enable-chunked-prefill \
  > qwen3-30b_server_dynamic_base_server.log 2>&1 &

SERVER_PID=$!

# 等待服务端口就绪
echo "等待服务端口 $PORT 就绪..."
# 使用curl替代nc，避免nc相关的not found打印
while ! curl -s --head http://localhost:$PORT/v1/models > /dev/null; do
  sleep 2
done
echo "服务已启动，开始发送请求..."

# 2. 依次发送请求，参数一一对应
for combination in "${COMBINATIONS[@]}"; do
  acquire_lock
  read -r VALUE RANDOM_INPUT_LEN RANDOM_OUTPUT_LEN <<< "$combination"
  echo "Running: num-prompts=$VALUE, max-concurrency=$VALUE, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN" | tee -a $LOGFILE
  HIP_VISIBLE_DEVICES=6,7 python3 /home/<USER>/benchmarks/benchmark_serving.py \
    --model $MODEL \
    --tokenizer $TOKENIZER \
    --random-input-len $RANDOM_INPUT_LEN \
    --random-output-len $RANDOM_OUTPUT_LEN \
    --num-prompts $((VALUE * 10))\
    --max-concurrency $VALUE \
    $IGNORE_EOS \
    --dataset-name $DATASET_NAME \
    --random-range-ratio 0.11 \
    --port $PORT \
    >> $LOGFILE 2>&1
  echo "---------------------------------------------" | tee -a $LOGFILE
  release_lock
done

# 3. 结束后关闭服务
echo "测试完成，关闭服务..."
kill $SERVER_PID
