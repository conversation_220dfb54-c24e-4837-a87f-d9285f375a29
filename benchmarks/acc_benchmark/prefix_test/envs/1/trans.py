#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import csv
import sys
from datetime import datetime

def extract_prefix_cache_test_data(log_file: str) -> list:
    """
    专门提取"阶段2: Test - 测量prefix cache性能"的性能数据
    """
    results = []

    # 读取文件
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 按照INFO分割，每个INFO开始一个新的测试配置
    # 使用更通用的正则表达式来匹配INFO时间戳
    import re
    sections = re.split(r'INFO \d{2}-\d{2}', content)[1:]  # 跳过第一个空的部分

    for section in sections:
        # 提取配置信息
        concurrency_match = re.search(r'max_concurrency=(\d+)', section)
        num_prompts_match = re.search(r'num_prompts=(\d+)', section)
        hit_rate_match = re.search(r'目标cache命中率: ([\d\.]+)', section)

        # 查找阶段2的结果
        phase2_match = re.search(r'阶段2: Test - 测量prefix cache性能.*?============ Serving Benchmark Result ============(.*?)(?=INFO|$)', section, re.DOTALL)

        if concurrency_match and num_prompts_match and hit_rate_match and phase2_match:
            result_section = phase2_match.group(1)

            # 提取性能指标
            total_token_match = re.search(r'Total Token throughput \(tok/s\):\s+([\d\.]+)', result_section)
            output_token_match = re.search(r'Output token throughput \(tok/s\):\s+([\d\.]+)', result_section)
            ttft_match = re.search(r'Mean TTFT \(ms\):\s+([\d\.]+)', result_section)
            tpot_match = re.search(r'Mean TPOT \(ms\):\s+([\d\.]+)', result_section)

            if all([total_token_match, output_token_match, ttft_match, tpot_match]):
                results.append({
                    'concurrency': concurrency_match.group(1),
                    'num_prompts': num_prompts_match.group(1),
                    'hit_rate': hit_rate_match.group(1),
                    'total_token_throughput': total_token_match.group(1),
                    'output_token_throughput': output_token_match.group(1),
                    'mean_ttft': ttft_match.group(1),
                    'mean_tpot': tpot_match.group(1)
                })

    return results

def process_log_to_csv_with_blanks(log_file: str) -> str:
    """
    将单个 .log 文件转换为 CSV，并在每个批次段前插入空行。
    """
    csv_file = log_file.replace('.log', '.csv')
    results = []

    # 正则模板
    run_re = re.compile(
        r"Running: num-prompts=(\d+), max-concurrency=\d+, input-len=(\d+), output-len=(\d+)"
    )
    total_token_re  = re.compile(r"Total Token throughput \(tok/s\):\s+([\d\.]+)")
    output_token_re = re.compile(r"Output token throughput \(tok/s\):\s+([\d\.]+)")
    mean_ttft_re    = re.compile(r"Mean TTFT \(ms\):\s+([\d\.]+)")
    mean_tpot_re    = re.compile(r"Mean TPOT \(ms\):\s+([\d\.]+)")

    last_bs = None
    first_record_seen = False

    # 读取文件
    with open(log_file, 'r', encoding='utf-8') as f:        # 利用上下文管理器保证文件正确关闭
        lines = f.readlines()

    i = 0
    while i < len(lines):
        m = run_re.search(lines[i])
        if m:
            num_prompts, input_len, output_len = m.groups()
            total_token = output_token = mean_ttft = mean_tpot = ''

            # 在后续 100 行内抓取统计指标
            for j in range(i, min(i + 1000, len(lines))):
                if not total_token:
                    m1 = total_token_re.search(lines[j])
                    if m1:
                        total_token = m1.group(1)
                if not output_token:
                    m2 = output_token_re.search(lines[j])
                    if m2:
                        output_token = m2.group(1)
                if not mean_ttft:
                    m3 = mean_ttft_re.search(lines[j])
                    if m3:
                        mean_ttft = m3.group(1)
                if not mean_tpot:
                    m4 = mean_tpot_re.search(lines[j])
                    if m4:
                        mean_tpot = m4.group(1)
                if all([total_token, output_token, mean_ttft, mean_tpot]):
                    break

            # ① 首条记录前插入空行
            # if not first_record_seen:
            #     results.append([''] * 7)
            #     first_record_seen = True

            # # ② 当批次大小变化时再次插入空行
            # if last_bs is not None and num_prompts != last_bs:
            #     results.append([''] * 7)

            # ③ 写入当前记录
            results.append([
                num_prompts,
                input_len,
                output_len,
                total_token,
                output_token,
                mean_ttft,
                mean_tpot
            ])
            last_bs = num_prompts
        i += 1

    # 写出 CSV
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow([
            "num-prompts",
            "input-len",
            "output-len",
            "Total Token throughput (tok/s):",
            "Output token throughput (tok/s):",
            "Mean TTFT (ms):",
            "Mean TPOT (ms):"
        ])
        writer.writerows(results)

    return csv_file

def create_prefix_cache_table(data: list) -> str:
    """
    将提取的prefix cache测试数据格式化为表格（按并发数分组）
    """
    if not data:
        return "未找到任何阶段2测试数据"

    # 按并发数分组
    concurrency_groups = {}
    for item in data:
        conc = item['concurrency']
        if conc not in concurrency_groups:
            concurrency_groups[conc] = []
        concurrency_groups[conc].append(item)

    # 创建表格
    table = []
    table.append("# Prefix Cache性能测试结果 (按并发数分组)")
    table.append("")

    for conc in sorted(concurrency_groups.keys(), key=int):
        table.append(f"## 并发数: {conc}")
        table.append("| Cache命中率 | Total Token吞吐量(tok/s) | Output Token吞吐量(tok/s) | Mean TTFT(ms) | Mean TPOT(ms) |")
        table.append("|-------------|-------------------------|---------------------------|---------------|---------------|")

        # 按命中率排序
        items = sorted(concurrency_groups[conc], key=lambda x: float(x['hit_rate']))
        for item in items:
            table.append(f"| {item['hit_rate']} | {item['total_token_throughput']} | {item['output_token_throughput']} | {item['mean_ttft']} | {item['mean_tpot']} |")
        table.append("")

    return "\n".join(table)

def create_csv_format(data: list, log_file: str) -> str:
    """
    将数据转换为指定的CSV格式
    """
    csv_file = log_file.replace('.log', '_prefix_cache_results.csv')

    # CSV头部
    headers = [
        "精度", "命中率", "batch", "输入token数", "输出token数",
        "计算输入+输出的吞吐量", "只计算输出的吞吐量",
        "avg_first_token_ms(ttft)", "mean tpot(ms)", "平均单路输出的吞吐量(tok/s)"
    ]

    # 准备数据行
    rows = []

    # 按并发数和命中率排序
    sorted_data = sorted(data, key=lambda x: (int(x['concurrency']), float(x['hit_rate'])))

    for item in sorted_data:
        # 转换命中率格式 (0.0 -> 0, 0.5 -> 50, 1.0 -> 100)
        hit_rate = int(float(item['hit_rate']) * 100)

        # 计算平均单路输出吞吐量 = 1000 / mean_tpot_ms
        avg_single_throughput = 1000.0 / float(item['mean_tpot'])

        row = [
            "float16",  # 精度
            hit_rate,   # 命中率
            item['concurrency'],  # batch
            4096,       # 输入token数
            1,          # 输出token数
            item['total_token_throughput'],    # 计算输入+输出的吞吐量
            item['output_token_throughput'],   # 只计算输出的吞吐量
            item['mean_ttft'],                 # avg_first_token_ms(ttft)
            item['mean_tpot'],                 # mean tpot(ms)
            f"{avg_single_throughput:.2f}"     # 平均单路输出的吞吐量(tok/s)
        ]
        rows.append(row)

    # 写入CSV文件
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(rows)

    return csv_file

def analyze_prefix_cache_log(log_file: str):
    """
    分析prefix cache日志文件并生成报告
    """
    print(f"正在分析日志文件: {log_file}")
    print("=" * 80)

    # 提取阶段2测试数据
    test_data = extract_prefix_cache_test_data(log_file)

    if not test_data:
        print("❌ 未找到任何'阶段2: Test - 测量prefix cache性能'的数据")
        return

    print(f"✅ 成功提取到 {len(test_data)} 条阶段2测试数据")
    print()

    # 生成表格
    table = create_prefix_cache_table(test_data)
    print("📊 Prefix Cache性能测试结果:")
    print()
    print(table)
    print()

    # 生成CSV文件
    csv_file = create_csv_format(test_data, log_file)
    print(f"📄 CSV格式结果已保存到: {csv_file}")

    # 保存到文件
    output_file = log_file.replace('.log', '_prefix_cache_analysis.txt')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Prefix Cache性能测试分析报告\n")
        f.write(f"源文件: {log_file}\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        f.write(table)
        f.write("\n\n")

        # 添加详细数据
        f.write("详细数据:\n")
        f.write("-" * 40 + "\n")
        for i, row in enumerate(test_data, 1):
            f.write(f"测试 {i}:\n")
            f.write(f"  并发数: {row['concurrency']}\n")
            f.write(f"  请求数: {row['num_prompts']}\n")
            f.write(f"  Cache命中率: {row['hit_rate']}\n")
            f.write(f"  Total Token吞吐量: {row['total_token_throughput']} tok/s\n")
            f.write(f"  Output Token吞吐量: {row['output_token_throughput']} tok/s\n")
            f.write(f"  Mean TTFT: {row['mean_ttft']} ms\n")
            f.write(f"  Mean TPOT: {row['mean_tpot']} ms\n")
            f.write("\n")

    print(f"📄 详细分析报告已保存到: {output_file}")

def main() -> None:
    # 扫描当前目录下的所有 .log 文件
    log_files = [fn for fn in os.listdir('.') if fn.endswith('.log')]
    for log_file in log_files:
        csv_path = process_log_to_csv_with_blanks(log_file)
        print(f"{log_file} → {csv_path} 完成")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python trans.py <log_file>")
        print("示例: python trans.py qwen_fix_serving_request.log")
        sys.exit(1)

    log_file = sys.argv[1]

    # 检查文件是否存在
    if not os.path.exists(log_file):
        print(f"❌ 文件不存在: {log_file}")
        sys.exit(1)

    # 分析prefix cache日志
    analyze_prefix_cache_log(log_file)
