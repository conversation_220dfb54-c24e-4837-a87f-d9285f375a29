#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import csv
import sys
from datetime import datetime

def extract_prefix_cache_test_data(log_file: str) -> list:
    """
    专门提取"阶段2: Test - 测量prefix cache性能"的性能数据
    """
    results = []

    # 读取文件
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 动态识别日期格式，按照INFO分割
    import re
    info_pattern = re.compile(r'INFO \d{2}-\d{2}')
    info_matches = list(info_pattern.finditer(content))

    if not info_matches:
        print("❌ 未找到任何INFO日志条目")
        return results

    # 按INFO位置分割内容
    sections = []
    for i, match in enumerate(info_matches):
        start_pos = match.start()
        end_pos = info_matches[i + 1].start() if i + 1 < len(info_matches) else len(content)
        sections.append(content[start_pos:end_pos])

    for section in sections:
        # 提取配置信息
        concurrency_match = re.search(r'max_concurrency=(\d+)', section)
        num_prompts_match = re.search(r'num_prompts=(\d+)', section)
        hit_rate_match = re.search(r'目标cache命中率: ([\d\.]+)', section)

        # 查找阶段2的结果
        phase2_match = re.search(r'阶段2: Test - 测量prefix cache性能.*?============ Serving Benchmark Result ============(.*?)(?====|$)', section, re.DOTALL)

        if concurrency_match and num_prompts_match and hit_rate_match and phase2_match:
            result_section = phase2_match.group(1)

            # 提取性能指标
            total_token_match = re.search(r'Total Token throughput \(tok/s\):\s+([\d\.]+)', result_section)
            output_token_match = re.search(r'Output token throughput \(tok/s\):\s+([\d\.]+)', result_section)
            ttft_match = re.search(r'Mean TTFT \(ms\):\s+([\d\.]+)', result_section)
            tpot_match = re.search(r'Mean TPOT \(ms\):\s+([\d\.]+)', result_section)

            if all([total_token_match, output_token_match, ttft_match, tpot_match]):
                results.append({
                    'concurrency': concurrency_match.group(1),
                    'num_prompts': num_prompts_match.group(1),
                    'hit_rate': hit_rate_match.group(1),
                    'total_token_throughput': total_token_match.group(1),
                    'output_token_throughput': output_token_match.group(1),
                    'mean_ttft': ttft_match.group(1),
                    'mean_tpot': tpot_match.group(1)
                })

    return results

def process_log_to_csv_with_blanks(log_file: str) -> str:
    """
    将单个 .log 文件转换为 CSV，并在每个批次段前插入空行。
    """
    csv_file = log_file.replace('.log', '.csv')
    results = []

    # 正则模板
    run_re = re.compile(
        r"Running: num-prompts=(\d+), max-concurrency=\d+, input-len=(\d+), output-len=(\d+)"
    )
    total_token_re  = re.compile(r"Total Token throughput \(tok/s\):\s+([\d\.]+)")
    output_token_re = re.compile(r"Output token throughput \(tok/s\):\s+([\d\.]+)")
    mean_ttft_re    = re.compile(r"Mean TTFT \(ms\):\s+([\d\.]+)")
    mean_tpot_re    = re.compile(r"Mean TPOT \(ms\):\s+([\d\.]+)")

    last_bs = None
    first_record_seen = False

    # 读取文件
    with open(log_file, 'r', encoding='utf-8') as f:        # 利用上下文管理器保证文件正确关闭
        lines = f.readlines()

    i = 0
    while i < len(lines):
        m = run_re.search(lines[i])
        if m:
            num_prompts, input_len, output_len = m.groups()
            total_token = output_token = mean_ttft = mean_tpot = ''

            # 在后续 100 行内抓取统计指标
            for j in range(i, min(i + 1000, len(lines))):
                if not total_token:
                    m1 = total_token_re.search(lines[j])
                    if m1:
                        total_token = m1.group(1)
                if not output_token:
                    m2 = output_token_re.search(lines[j])
                    if m2:
                        output_token = m2.group(1)
                if not mean_ttft:
                    m3 = mean_ttft_re.search(lines[j])
                    if m3:
                        mean_ttft = m3.group(1)
                if not mean_tpot:
                    m4 = mean_tpot_re.search(lines[j])
                    if m4:
                        mean_tpot = m4.group(1)
                if all([total_token, output_token, mean_ttft, mean_tpot]):
                    break

            # ① 首条记录前插入空行
            # if not first_record_seen:
            #     results.append([''] * 7)
            #     first_record_seen = True

            # # ② 当批次大小变化时再次插入空行
            # if last_bs is not None and num_prompts != last_bs:
            #     results.append([''] * 7)

            # ③ 写入当前记录
            results.append([
                num_prompts,
                input_len,
                output_len,
                total_token,
                output_token,
                mean_ttft,
                mean_tpot
            ])
            last_bs = num_prompts
        i += 1

    # 写出 CSV
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow([
            "num-prompts",
            "input-len",
            "output-len",
            "Total Token throughput (tok/s):",
            "Output token throughput (tok/s):",
            "Mean TTFT (ms):",
            "Mean TPOT (ms):"
        ])
        writer.writerows(results)

    return csv_file

def create_prefix_cache_table(data: list) -> str:
    """
    将提取的prefix cache测试数据格式化为表格（按并发数分组）
    """
    if not data:
        return "未找到任何阶段2测试数据"

    # 按并发数分组
    concurrency_groups = {}
    for item in data:
        conc = item['concurrency']
        if conc not in concurrency_groups:
            concurrency_groups[conc] = []
        concurrency_groups[conc].append(item)

    # 创建表格
    table = []
    table.append("# Prefix Cache性能测试结果 (按并发数分组)")
    table.append("")

    for conc in sorted(concurrency_groups.keys(), key=int):
        table.append(f"## 并发数: {conc}")
        table.append("| Cache命中率 | Total Token吞吐量(tok/s) | Output Token吞吐量(tok/s) | Mean TTFT(ms) | Mean TPOT(ms) |")
        table.append("|-------------|-------------------------|---------------------------|---------------|---------------|")

        # 按命中率排序
        items = sorted(concurrency_groups[conc], key=lambda x: float(x['hit_rate']))
        for item in items:
            table.append(f"| {item['hit_rate']} | {item['total_token_throughput']} | {item['output_token_throughput']} | {item['mean_ttft']} | {item['mean_tpot']} |")
        table.append("")

    return "\n".join(table)

def create_csv_format(data: list, log_file: str) -> str:
    """
    将数据转换为指定的CSV格式
    """
    csv_file = log_file.replace('.log', '_prefix_cache_results.csv')

    # CSV头部
    headers = [
        "精度", "命中率", "batch", "输入token数", "输出token数",
        "计算输入+输出的吞吐量", "只计算输出的吞吐量",
        "avg_first_token_ms(ttft)", "mean tpot(ms)", "平均单路输出的吞吐量(tok/s)",
        "首字token提升率(%)"
    ]

    # 准备数据行
    rows = []

    # 按并发数和命中率排序
    sorted_data = sorted(data, key=lambda x: (int(x['concurrency']), float(x['hit_rate'])))

    # 按batch size分组，找到每个batch size的baseline (命中率=0)
    batch_baselines = {}
    for item in sorted_data:
        batch_size = item['concurrency']
        hit_rate = float(item['hit_rate'])
        if hit_rate == 0.0:  # 命中率为0的作为baseline
            batch_baselines[batch_size] = float(item['mean_ttft'])

    for item in sorted_data:
        # 转换命中率格式 (0.0 -> 0, 0.5 -> 50, 1.0 -> 100)
        hit_rate = int(float(item['hit_rate']) * 100)

        # 计算平均单路输出吞吐量 = 总输出吞吐量 / 并发数
        avg_single_throughput = float(item['output_token_throughput']) / int(item['concurrency'])

        # 计算首字token提升率
        batch_size = item['concurrency']
        current_ttft = float(item['mean_ttft'])

        if batch_size in batch_baselines and batch_baselines[batch_size] > 0:
            baseline_ttft = batch_baselines[batch_size]
            if hit_rate == 0:  # baseline自己的提升率为0
                improvement_rate = 0.0
            else:
                # 提升率 = (baseline - current) / baseline * 100%
                improvement_rate = (baseline_ttft - current_ttft) / baseline_ttft * 100
        else:
            improvement_rate = 0.0  # 如果找不到baseline，设为0

        row = [
            "float16",  # 精度
            hit_rate,   # 命中率
            item['concurrency'],  # batch
            4096,       # 输入token数
            1,          # 输出token数
            item['total_token_throughput'],    # 计算输入+输出的吞吐量
            item['output_token_throughput'],   # 只计算输出的吞吐量
            item['mean_ttft'],                 # avg_first_token_ms(ttft)
            item['mean_tpot'],                 # mean tpot(ms)
            f"{avg_single_throughput:.2f}",    # 平均单路输出的吞吐量(tok/s)
            f"{improvement_rate:.2f}"          # 首字token提升率(%)
        ]
        rows.append(row)

    # 写入CSV文件
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(rows)

    return csv_file

def add_improvement_column_to_existing_csv(csv_file: str) -> str:
    """
    为现有的CSV文件添加首字token提升率列
    """
    import pandas as pd

    # 读取现有CSV文件
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return csv_file

    # 检查必要的列是否存在
    required_columns = ['命中率', 'batch', 'avg_first_token_ms(ttft)']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ CSV文件缺少必要的列: {missing_columns}")
        return csv_file

    # 计算首字token提升率
    improvement_rates = []

    # 按batch size分组，找到每个batch size的baseline (命中率=0)
    batch_baselines = {}
    for _, row in df.iterrows():
        batch_size = row['batch']
        hit_rate = row['命中率']
        if hit_rate == 0:  # 命中率为0的作为baseline
            batch_baselines[batch_size] = row['avg_first_token_ms(ttft)']

    # 计算每行的提升率
    for _, row in df.iterrows():
        batch_size = row['batch']
        hit_rate = row['命中率']
        current_ttft = row['avg_first_token_ms(ttft)']

        if batch_size in batch_baselines and batch_baselines[batch_size] > 0:
            baseline_ttft = batch_baselines[batch_size]
            if hit_rate == 0:  # baseline自己的提升率为0
                improvement_rate = 0.0
            else:
                # 提升率 = (baseline - current) / baseline * 100%
                improvement_rate = (baseline_ttft - current_ttft) / baseline_ttft * 100
        else:
            improvement_rate = 0.0  # 如果找不到baseline，设为0

        improvement_rates.append(round(improvement_rate, 2))

    # 添加新列
    df['首字token提升率(%)'] = improvement_rates

    # 保存更新后的CSV文件
    output_file = csv_file.replace('.csv', '_with_improvement.csv')
    df.to_csv(output_file, index=False, encoding='utf-8')

    print(f"✅ 已添加首字token提升率列，保存到: {output_file}")

    # 显示结果预览
    print("\n📊 结果预览:")
    print(df[['命中率', 'batch', 'avg_first_token_ms(ttft)', '首字token提升率(%)']].to_string(index=False))

    return output_file

def analyze_prefix_cache_log(log_file: str):
    """
    分析prefix cache日志文件并生成报告
    """
    print(f"正在分析日志文件: {log_file}")
    print("=" * 80)

    # 提取阶段2测试数据
    test_data = extract_prefix_cache_test_data(log_file)

    if not test_data:
        print("❌ 未找到任何'阶段2: Test - 测量prefix cache性能'的数据")
        return

    print(f"✅ 成功提取到 {len(test_data)} 条阶段2测试数据")
    print()

    # 生成表格
    table = create_prefix_cache_table(test_data)
    print("📊 Prefix Cache性能测试结果:")
    print()
    print(table)
    print()

    # 生成CSV文件
    csv_file = create_csv_format(test_data, log_file)
    print(f"📄 CSV格式结果已保存到: {csv_file}")

    # 保存到文件
    output_file = log_file.replace('.log', '_prefix_cache_analysis.txt')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Prefix Cache性能测试分析报告\n")
        f.write(f"源文件: {log_file}\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        f.write(table)
        f.write("\n\n")

        # 添加详细数据
        f.write("详细数据:\n")
        f.write("-" * 40 + "\n")
        for i, row in enumerate(test_data, 1):
            f.write(f"测试 {i}:\n")
            f.write(f"  并发数: {row['concurrency']}\n")
            f.write(f"  请求数: {row['num_prompts']}\n")
            f.write(f"  Cache命中率: {row['hit_rate']}\n")
            f.write(f"  Total Token吞吐量: {row['total_token_throughput']} tok/s\n")
            f.write(f"  Output Token吞吐量: {row['output_token_throughput']} tok/s\n")
            f.write(f"  Mean TTFT: {row['mean_ttft']} ms\n")
            f.write(f"  Mean TPOT: {row['mean_tpot']} ms\n")
            f.write("\n")

    print(f"📄 详细分析报告已保存到: {output_file}")

def main() -> None:
    # 扫描当前目录下的所有 .log 文件
    log_files = [fn for fn in os.listdir('.') if fn.endswith('.log')]
    for log_file in log_files:
        csv_path = process_log_to_csv_with_blanks(log_file)
        print(f"{log_file} → {csv_path} 完成")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  处理日志文件: python trans.py <log_file>")
        print("  处理CSV文件: python trans.py --csv <csv_file>")
        print("示例:")
        print("  python trans.py qwen_fix_serving_request.log")
        print("  python trans.py --csv qwen_fix_serving_request_prefix_cache_results.csv")
        sys.exit(1)

    if sys.argv[1] == "--csv":
        # 处理CSV文件模式
        if len(sys.argv) != 3:
            print("❌ CSV模式需要指定CSV文件路径")
            print("Usage: python trans.py --csv <csv_file>")
            sys.exit(1)

        csv_file = sys.argv[2]

        # 检查文件是否存在
        if not os.path.exists(csv_file):
            print(f"❌ 文件不存在: {csv_file}")
            sys.exit(1)

        # 为现有CSV添加提升率列
        add_improvement_column_to_existing_csv(csv_file)
    else:
        # 处理日志文件模式
        log_file = sys.argv[1]

        # 检查文件是否存在
        if not os.path.exists(log_file):
            print(f"❌ 文件不存在: {log_file}")
            sys.exit(1)

        # 分析prefix cache日志
        analyze_prefix_cache_log(log_file)
