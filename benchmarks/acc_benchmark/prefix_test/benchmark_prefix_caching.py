# SPDX-License-Identifier: Apache-2.0
r"""Benchmark online serving throughput.

On the server side, run one of the following commands:
    vLLM OpenAI API server
    vllm serve <your_model> \
        --swap-space 16 \
        --disable-log-requests

On the client side, run:
    python benchmarks/benchmark_serving.py \
        --backend <backend> \
        --model <your_model> \
        --dataset-name sharegpt \
        --dataset-path <path to dataset> \
        --request-rate <request_rate> # By default <request_rate> is inf
        --num-prompts <num_prompts> # By default <num_prompts> is 1000

    when using tgi backend, add
        --endpoint /generate_stream
    to the end of the command above.
"""
import argparse
import asyncio
import gc
import json
import os
import random
import time
import warnings
from collections.abc import AsyncGenerator, Iterable
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Optional, Union

import numpy as np
from backend_request_func import (ASYNC_REQUEST_FUNCS,
                                  OPENAI_COMPATIBLE_BACKENDS, RequestFuncInput,
                                  RequestFuncOutput, reset_prefix_cache)
from tqdm.asyncio import tqdm
from transformers.tokenization_utils_base import PreTrainedTokenizerBase

try:
    from vllm.transformers_utils.tokenizer import get_tokenizer, AnyTokenizer
except ImportError:
    from backend_request_func import get_tokenizer
    AnyTokenizer = PreTrainedTokenizerBase

try:
    from vllm.utils import FlexibleArgumentParser
except ImportError:
    from argparse import ArgumentParser as FlexibleArgumentParser

from benchmark_dataset import (AIMODataset, ASRDataset, BurstGPTDataset,
                               ConversationDataset, HuggingFaceDataset,
                               InstructCoderDataset, RandomDataset,
                               SampleRequest, ShareGPTDataset, SonnetDataset,
                               VisionArenaDataset)
from benchmark_utils import convert_to_pytorch_benchmark_format, write_to_json
import logging
MILLISECONDS_TO_SECONDS_CONVERSION = 1000


def generate_prefix_cache_dataset(
    tokenizer: Any,
    num_requests: int,
    target_hit_rate: float,
    input_len: int,
    output_len: int,
    seed: int = 0,
    warmup_requests: Optional[list[SampleRequest]] = None,
    mode: str = "warmup"  # "warmup" or "test"
) -> list[SampleRequest]:
    """生成可控cache hit rate的数据集
    
    使用精准的prefix长度控制方法：
    - Warmup模式：生成完全不同的确定性序列
    - Test模式：每个请求 = warmup的前N个token + 新的suffix
    - 其中 N = input_len * target_hit_rate，直接控制token级别的重用
    
    Args:
        tokenizer: 分词器
        num_requests: 请求总数
        target_hit_rate: 目标命中率 (0.0-1.0)，控制prefix长度比例
        input_len: 输入长度
        output_len: 输出长度
        seed: 随机种子
        warmup_requests: warmup阶段的请求列表（仅在test模式下使用）
        mode: "warmup" 或 "test"
        
    Returns:
        list[SampleRequest]: 生成的请求列表
    """
    np.random.seed(seed)
    vocab_size = getattr(tokenizer, 'vocab_size', 50000)
    if not isinstance(vocab_size, int):
        vocab_size = 50000
    
    if mode == "warmup":
        # Warmup阶段：生成完全不同的确定性序列，避免任何共同前缀
        print(f"生成warmup数据集：{num_requests}个确定性的不同序列")
        requests = []
        
        for i in range(num_requests):
            # 生成确定性的唯一序列，不使用随机数
            # 方法：为每个序列创建唯一的开头，然后用规律性的数字填充
            
            # 创建唯一标识符
            unique_id = f"WARMUP_SEQ_{i:06d}"
            
            # 将标识符转换为数字序列
            id_numbers = [ord(c) for c in unique_id]
            
            # 生成足够长度的确定性序列
            full_tokens = []
            
            # 先加入唯一标识符的字符编码
            full_tokens.extend(id_numbers)
            
            # 用确定性的模式填充剩余位置
            remaining_len = input_len - len(id_numbers)
            if remaining_len > 0:
                # 使用序列索引和位置生成确定性的token值
                for j in range(remaining_len):
                    # 确保不同序列产生不同的token，且在词汇表范围内
                    token_val = ((i * 1000 + j * 7 + seed) % vocab_size)
                    # 避免特殊token（通常是前几个）
                    if token_val < 10:
                        token_val += 10
                    full_tokens.append(token_val)
            
            # 截断到指定长度
            full_tokens = full_tokens[:input_len]
            
            # 如果长度不够，用序列特定的值填充
            while len(full_tokens) < input_len:
                fill_val = ((i * 13 + len(full_tokens) * 17 + seed) % vocab_size)
                if fill_val < 10:
                    fill_val += 10
                full_tokens.append(fill_val)
            
            try:
                prompt = tokenizer.decode(full_tokens)
            except:
                # 如果decode失败，使用文本方式
                prompt = f"WARMUP_UNIQUE_SEQUENCE_{i:06d} " + " ".join([f"word_{j:03d}" for j in range(input_len//4)])
                prompt = prompt[:input_len*4]  # 粗略控制长度
            
            requests.append(SampleRequest(
                prompt=prompt,
                prompt_len=input_len,
                expected_output_len=output_len
            ))
        
        return requests
    
    elif mode == "test":
        # Test阶段：基于warmup请求生成具有控制命中率的序列
        if warmup_requests is None:
            raise ValueError("Test模式需要提供warmup_requests")
        
        print(f"生成test数据集：{num_requests}个请求，精准prefix控制")
        
        # 方法1：精准的prefix长度控制
        # 每个请求都有相同长度的prefix（来自warmup）+ 新的suffix
        prefix_len = int(input_len * target_hit_rate)
        suffix_len = input_len - prefix_len
        
        print(f"  - 目标命中率: {target_hit_rate:.1%}")
        print(f"  - Prefix长度 (重用): {prefix_len} tokens")
        print(f"  - Suffix长度 (新生成): {suffix_len} tokens")
        print(f"  - 理论命中率: {prefix_len/input_len:.1%}")
        print(f"  - 所有{num_requests}个请求都采用相同的prefix+suffix结构")
        
        requests = []
        
        # 所有请求都使用prefix+suffix结构
        for i in range(num_requests):
            # 选择要重用的warmup请求（循环使用）
            warmup_idx = i % len(warmup_requests)
            warmup_request = warmup_requests[warmup_idx]
            
            if prefix_len == 0:
                # 完全不重用，生成全新序列
                unique_id = f"TEST_NEW_{i:06d}"
                id_numbers = [ord(c) for c in unique_id]
                
                full_tokens = []
                full_tokens.extend(id_numbers)
                
                remaining_len = input_len - len(id_numbers)
                for j in range(remaining_len):
                    token_val = ((i * 2000 + j * 11 + seed + 50000) % vocab_size)
                    if token_val < 10:
                        token_val += 10
                    full_tokens.append(token_val)
                
                full_tokens = full_tokens[:input_len]
                while len(full_tokens) < input_len:
                    fill_val = ((i * 19 + len(full_tokens) * 23 + seed + 50000) % vocab_size)
                    if fill_val < 10:
                        fill_val += 10
                    full_tokens.append(fill_val)
                
                try:
                    prompt = tokenizer.decode(full_tokens)
                except:
                    prompt = f"TEST_NEW_SEQUENCE_{i:06d} " + " ".join([f"new_{j:03d}" for j in range(input_len//4)])
                    prompt = prompt[:input_len*4]
                    
            elif prefix_len >= input_len:
                # 完全重用
                prompt = warmup_request.prompt
                
            else:
                # 部分重用：prefix来自warmup，suffix是新的
                try:
                    # 获取warmup请求的tokens
                    warmup_tokens = tokenizer.encode(warmup_request.prompt, add_special_tokens=False)
                    
                    # 取前prefix_len个token作为prefix
                    prefix_tokens = warmup_tokens[:prefix_len]
                    
                    # 生成新的suffix
                    suffix_tokens = []
                    for j in range(suffix_len):
                        token_val = ((i * 3000 + j * 13 + seed + 100000) % vocab_size)
                        if token_val < 10:
                            token_val += 10
                        suffix_tokens.append(token_val)
                    
                    # 组合prefix和suffix
                    combined_tokens = prefix_tokens + suffix_tokens
                    combined_tokens = combined_tokens[:input_len]
                    
                    # 如果长度不足，用suffix pattern填充
                    while len(combined_tokens) < input_len:
                        fill_val = ((i * 21 + len(combined_tokens) * 29 + seed + 100000) % vocab_size)
                        if fill_val < 10:
                            fill_val += 10
                        combined_tokens.append(fill_val)
                    
                    prompt = tokenizer.decode(combined_tokens)
                    
                except:
                    # 如果tokenize失败，使用文本拼接
                    warmup_text = warmup_request.prompt
                    prefix_text = warmup_text[:int(len(warmup_text) * target_hit_rate)]
                    suffix_text = f" SUFFIX_{i:06d} " + " ".join([f"suf_{j:03d}" for j in range(suffix_len//4)])
                    prompt = (prefix_text + suffix_text)[:input_len*4]
            
            requests.append(SampleRequest(
                prompt=prompt,
                prompt_len=input_len,
                expected_output_len=output_len
            ))
        
        return requests
    
    else:
        raise ValueError(f"未知的模式: {mode}")


@dataclass
class BenchmarkMetrics:
    completed: int
    total_input: int
    total_output: int
    request_throughput: float
    request_goodput: float
    output_throughput: float
    total_token_throughput: float
    mean_ttft_ms: float
    median_ttft_ms: float
    std_ttft_ms: float
    percentiles_ttft_ms: list[tuple[float, float]]
    mean_tpot_ms: float
    median_tpot_ms: float
    std_tpot_ms: float
    percentiles_tpot_ms: list[tuple[float, float]]
    mean_itl_ms: float
    median_itl_ms: float
    std_itl_ms: float
    percentiles_itl_ms: list[tuple[float, float]]
    # E2EL stands for end-to-end latency per request.
    # It is the time taken on the client side from sending
    # a request to receiving a complete response.
    mean_e2el_ms: float
    median_e2el_ms: float
    std_e2el_ms: float
    percentiles_e2el_ms: list[tuple[float, float]]


async def get_request(
    input_requests: list[SampleRequest],
    request_rate: float,
    burstiness: float = 1.0,
) -> AsyncGenerator[SampleRequest, None]:
    """
    Asynchronously generates requests at a specified rate
    with OPTIONAL burstiness.

    Args:
        input_requests:
            A list of input requests, each represented as a SampleRequest.
        request_rate:
            The rate at which requests are generated (requests/s).
        burstiness (optional):
            The burstiness factor of the request generation.
            Only takes effect when request_rate is not inf.
            Default value is 1, which follows a Poisson process.
            Otherwise, the request intervals follow a gamma distribution.
            A lower burstiness value (0 < burstiness < 1) results
            in more bursty requests, while a higher burstiness value
            (burstiness > 1) results in a more uniform arrival of requests.
    """
    request_iterator: Iterable[SampleRequest] = iter(input_requests)

    # Calculate scale parameter theta to maintain the desired request_rate.
    assert burstiness > 0, (
        f"A positive burstiness factor is expected, but given {burstiness}.")
    theta = 1.0 / (request_rate * burstiness)

    for request in request_iterator:
        yield request

        if request_rate == float("inf"):
            # If the request rate is infinity, then we don't need to wait.
            continue

        # Sample the request interval from the gamma distribution.
        # If burstiness is 1, it follows exponential distribution.
        interval = np.random.gamma(shape=burstiness, scale=theta)
        # The next request will be sent after the interval.
        await asyncio.sleep(interval)


def calculate_metrics(
    input_requests: list[SampleRequest],
    outputs: list[RequestFuncOutput],
    dur_s: float,
    tokenizer: PreTrainedTokenizerBase,
    selected_percentile_metrics: list[str],
    selected_percentiles: list[float],
    goodput_config_dict: dict[str, float],
) -> tuple[BenchmarkMetrics, list[int]]:
    actual_output_lens: list[int] = []
    total_input = 0
    completed = 0
    good_completed = 0
    itls: list[float] = []
    tpots: list[float] = []
    all_tpots: list[float] = []
    ttfts: list[float] = []
    e2els: list[float] = []
    for i in range(len(outputs)):
        if outputs[i].success:
            output_len = outputs[i].output_tokens

            if not output_len:
                # We use the tokenizer to count the number of output tokens
                # for some serving backends instead of looking at
                # len(outputs[i].itl) since multiple output tokens may be
                # bundled together
                # Note : this may inflate the output token count slightly
                output_len = len(
                    tokenizer(outputs[i].generated_text,
                              add_special_tokens=False).input_ids)
            actual_output_lens.append(output_len)
            total_input += input_requests[i].prompt_len
            tpot = 0
            if output_len > 1:
                latency_minus_ttft = outputs[i].latency - outputs[i].ttft
                tpot = latency_minus_ttft / (output_len - 1)
                tpots.append(tpot)
            # Note: if output_len <= 1, we regard tpot as 0 for goodput
            all_tpots.append(tpot)
            itls += outputs[i].itl
            ttfts.append(outputs[i].ttft)
            e2els.append(outputs[i].latency)
            completed += 1
        else:
            actual_output_lens.append(0)

    if goodput_config_dict:
        valid_metrics = []
        slo_values = []

        if "ttft" in goodput_config_dict:
            valid_metrics.append(ttfts)
            slo_values.append(goodput_config_dict["ttft"] /
                              MILLISECONDS_TO_SECONDS_CONVERSION)
        if "tpot" in goodput_config_dict:
            valid_metrics.append(all_tpots)
            slo_values.append(goodput_config_dict["tpot"] /
                              MILLISECONDS_TO_SECONDS_CONVERSION)
        if "e2el" in goodput_config_dict:
            valid_metrics.append(e2els)
            slo_values.append(goodput_config_dict["e2el"] /
                              MILLISECONDS_TO_SECONDS_CONVERSION)

        for req_metric in zip(*valid_metrics):
            is_good_req = all([s >= r for s, r in zip(slo_values, req_metric)])
            if is_good_req:
                good_completed += 1

    if completed == 0:
        warnings.warn(
            "All requests failed. This is likely due to a misconfiguration "
            "on the benchmark arguments.",
            stacklevel=2)
    metrics = BenchmarkMetrics(
        completed=completed,
        total_input=total_input,
        total_output=sum(actual_output_lens),
        request_throughput=completed / dur_s,
        request_goodput=good_completed / dur_s,
        output_throughput=sum(actual_output_lens) / dur_s,
        total_token_throughput=(total_input + sum(actual_output_lens)) / dur_s,
        mean_ttft_ms=float(np.mean(ttfts or 0)) *
        1000,  # ttfts is empty if streaming is not supported by backend
        std_ttft_ms=float(np.std(ttfts or 0)) * 1000,
        median_ttft_ms=float(np.median(ttfts or 0)) * 1000,
        percentiles_ttft_ms=[(p, float(np.percentile(ttfts or 0, p)) * 1000)
                             for p in selected_percentiles],
        mean_tpot_ms=float(np.mean(tpots or 0)) * 1000,
        std_tpot_ms=float(np.std(tpots or 0)) * 1000,
        median_tpot_ms=float(np.median(tpots or 0)) * 1000,
        percentiles_tpot_ms=[(p, float(np.percentile(tpots or 0, p)) * 1000)
                             for p in selected_percentiles],
        mean_itl_ms=float(np.mean(itls or 0)) * 1000,
        std_itl_ms=float(np.std(itls or 0)) * 1000,
        median_itl_ms=float(np.median(itls or 0)) * 1000,
        percentiles_itl_ms=[(p, float(np.percentile(itls or 0, p)) * 1000)
                            for p in selected_percentiles],
        mean_e2el_ms=float(np.mean(e2els or 0)) * 1000,
        std_e2el_ms=float(np.std(e2els or 0)) * 1000,
        median_e2el_ms=float(np.median(e2els or 0)) * 1000,
        percentiles_e2el_ms=[(p, float(np.percentile(e2els or 0, p)) * 1000)
                             for p in selected_percentiles],
    )

    return metrics, actual_output_lens


async def benchmark(
    backend: str,
    api_url: str,
    base_url: str,
    model_id: str,
    model_name: str,
    tokenizer: PreTrainedTokenizerBase,
    input_requests: list[SampleRequest],
    logprobs: Optional[int],
    request_rate: float,
    burstiness: float,
    disable_tqdm: bool,
    profile: bool,
    selected_percentile_metrics: list[str],
    selected_percentiles: list[float],
    ignore_eos: bool,
    goodput_config_dict: dict[str, float],
    max_concurrency: Optional[int],
    lora_modules: Optional[Iterable[str]],
    extra_body: Optional[dict],
    reset_cache_before_test: bool = False,
):
    if backend in ASYNC_REQUEST_FUNCS:
        request_func = ASYNC_REQUEST_FUNCS[backend]
    else:
        raise ValueError(f"Unknown backend: {backend}")

    # 如果需要，在测试前重置prefix cache
    if reset_cache_before_test:
        logging.info("重置Prefix Cache...")
        reset_success = await reset_prefix_cache(base_url)
        if not reset_success:
            print("警告: 无法重置prefix cache，可能影响测试结果")
            print("请确保vLLM服务启动时设置了 VLLM_SERVER_DEV_MODE=1")

    print("Starting initial single prompt test run...")
    test_prompt, test_prompt_len, test_output_len, test_mm_content = \
        input_requests[0].prompt, input_requests[0].prompt_len, \
        input_requests[0].expected_output_len, \
            input_requests[0].multi_modal_data

    assert test_mm_content is None or isinstance(test_mm_content, dict)
    test_input = RequestFuncInput(
        model=model_id,
        model_name=model_name,
        prompt=test_prompt,
        api_url=api_url,
        prompt_len=test_prompt_len,
        output_len=test_output_len,
        logprobs=logprobs,
        multi_modal_content=test_mm_content,
        ignore_eos=ignore_eos,
        extra_body=extra_body,
    )

    test_output = await request_func(request_func_input=test_input)
    if not test_output.success:
        raise ValueError(
            "Initial test run failed - Please make sure benchmark arguments "
            f"are correctly specified. Error: {test_output.error}")
    else:
        print("Initial test run completed. Starting main benchmark run...")

    if lora_modules:
        # For each input request, choose a LoRA module at random.
        lora_modules_list = list(lora_modules)
        lora_modules = iter(
            [random.choice(lora_modules_list) \
                for _ in range(len(input_requests))])

    if profile:
        print("Starting profiler...")
        profile_input = RequestFuncInput(model=model_id,
                                         model_name=model_name,
                                         prompt=test_prompt,
                                         api_url=base_url + "/start_profile",
                                         prompt_len=test_prompt_len,
                                         output_len=test_output_len,
                                         logprobs=logprobs,
                                         multi_modal_content=test_mm_content,
                                         ignore_eos=ignore_eos,
                                         extra_body=extra_body)
        profile_output = await request_func(request_func_input=profile_input)
        if profile_output.success:
            print("Profiler started")

    if burstiness == 1.0:
        distribution = "Poisson process"
    else:
        distribution = "Gamma distribution"

    print(f"Traffic request rate: {request_rate}")
    print(f"Burstiness factor: {burstiness} ({distribution})")
    print(f"Maximum request concurrency: {max_concurrency}")

    pbar = None if disable_tqdm else tqdm(total=len(input_requests))

    # This can be used once the minimum Python version is 3.10 or higher,
    # and it will simplify the code in limited_request_func.
    #    semaphore = (asyncio.Semaphore(max_concurrency)
    #                 if max_concurrency else contextlib.nullcontext())
    semaphore = (asyncio.Semaphore(max_concurrency)
                 if max_concurrency else None)

    async def limited_request_func(request_func_input, pbar):
        if semaphore is None:
            return await request_func(request_func_input=request_func_input,
                                      pbar=pbar)
        async with semaphore:
            return await request_func(request_func_input=request_func_input,
                                      pbar=pbar)

    benchmark_start_time = time.perf_counter()
    tasks: list[asyncio.Task] = []
    async for request in get_request(input_requests, request_rate, burstiness):
        prompt, prompt_len, output_len, mm_content = request.prompt, \
            request.prompt_len, request.expected_output_len, \
                request.multi_modal_data
        req_model_id, req_model_name = model_id, model_name
        if lora_modules:
            req_lora_module = next(lora_modules)  # type: ignore
            req_model_id, req_model_name = req_lora_module, req_lora_module

        # 确保mm_content是正确的类型
        mm_content_dict = None
        if mm_content is not None:
            if hasattr(mm_content, '__iter__') and not isinstance(mm_content, str):
                mm_content_dict = dict(mm_content) if not isinstance(mm_content, dict) else mm_content
            else:
                mm_content_dict = mm_content

        request_func_input = RequestFuncInput(model=req_model_id,
                                              model_name=req_model_name,
                                              prompt=prompt,
                                              api_url=api_url,
                                              prompt_len=prompt_len,
                                              output_len=output_len,
                                              logprobs=logprobs,
                                              multi_modal_content=mm_content_dict,  # type: ignore
                                              ignore_eos=ignore_eos,
                                              extra_body=extra_body)
        tasks.append(
            asyncio.create_task(
                limited_request_func(request_func_input=request_func_input,
                                     pbar=pbar)))
    outputs: list[RequestFuncOutput] = await asyncio.gather(*tasks)

    if profile:
        print("Stopping profiler...")
        profile_input = RequestFuncInput(
            model=model_id,
            prompt=test_prompt,
            api_url=base_url + "/stop_profile",
            prompt_len=test_prompt_len,
            output_len=test_output_len,
            logprobs=logprobs,
        )
        profile_output = await request_func(request_func_input=profile_input)
        if profile_output.success:
            print("Profiler stopped")

    if pbar is not None:
        pbar.close()

    benchmark_duration = time.perf_counter() - benchmark_start_time

    metrics, actual_output_lens = calculate_metrics(
        input_requests=input_requests,
        outputs=outputs,
        dur_s=benchmark_duration,
        tokenizer=tokenizer,
        selected_percentile_metrics=selected_percentile_metrics,
        selected_percentiles=selected_percentiles,
        goodput_config_dict=goodput_config_dict,
    )

    print("{s:{c}^{n}}".format(s=' Serving Benchmark Result ', n=50, c='='))
    print("{:<40} {:<10}".format("Successful requests:", metrics.completed))
    print("{:<40} {:<10.2f}".format("Benchmark duration (s):",
                                    benchmark_duration))
    print("{:<40} {:<10}".format("Total input tokens:", metrics.total_input))
    print("{:<40} {:<10}".format("Total generated tokens:",
                                 metrics.total_output))
    print("{:<40} {:<10.2f}".format("Request throughput (req/s):",
                                    metrics.request_throughput))
    if goodput_config_dict:
        print("{:<40} {:<10.2f}".format("Request goodput (req/s):",
                                        metrics.request_goodput))
    print("{:<40} {:<10.2f}".format("Output token throughput (tok/s):",
                                    metrics.output_throughput))
    print("{:<40} {:<10.2f}".format("Total Token throughput (tok/s):",
                                    metrics.total_token_throughput))

    result = {
        "duration": benchmark_duration,
        "completed": metrics.completed,
        "total_input_tokens": metrics.total_input,
        "total_output_tokens": metrics.total_output,
        "request_throughput": metrics.request_throughput,
        "request_goodput:":
        metrics.request_goodput if goodput_config_dict else None,
        "output_throughput": metrics.output_throughput,
        "total_token_throughput": metrics.total_token_throughput,
        "input_lens": [output.prompt_len for output in outputs],
        "output_lens": actual_output_lens,
        "ttfts": [output.ttft for output in outputs],
        "itls": [output.itl for output in outputs],
        "generated_texts": [output.generated_text for output in outputs],
        "errors": [output.error for output in outputs],
    }

    def process_one_metric(
        # E.g., "ttft"
        metric_attribute_name: str,
        # E.g., "TTFT"
        metric_name: str,
        # E.g., "Time to First Token"
        metric_header: str,
    ):
        # This function prints and adds statistics of the specified
        # metric.
        if metric_attribute_name not in selected_percentile_metrics:
            return
        print("{s:{c}^{n}}".format(s=metric_header, n=50, c='-'))
        print("{:<40} {:<10.2f}".format(
            f"Mean {metric_name} (ms):",
            getattr(metrics, f"mean_{metric_attribute_name}_ms")))
        print("{:<40} {:<10.2f}".format(
            f"Median {metric_name} (ms):",
            getattr(metrics, f"median_{metric_attribute_name}_ms")))
        result[f"mean_{metric_attribute_name}_ms"] = getattr(
            metrics, f"mean_{metric_attribute_name}_ms")
        result[f"median_{metric_attribute_name}_ms"] = getattr(
            metrics, f"median_{metric_attribute_name}_ms")
        result[f"std_{metric_attribute_name}_ms"] = getattr(
            metrics, f"std_{metric_attribute_name}_ms")
        for p, value in getattr(metrics,
                                f"percentiles_{metric_attribute_name}_ms"):
            p_word = str(int(p)) if int(p) == p else str(p)
            print("{:<40} {:<10.2f}".format(f"P{p_word} {metric_name} (ms):",
                                            value))
            result[f"p{p_word}_{metric_attribute_name}_ms"] = value

    process_one_metric("ttft", "TTFT", "Time to First Token")
    process_one_metric("tpot", "TPOT",
                       "Time per Output Token (excl. 1st token)")
    process_one_metric("itl", "ITL", "Inter-token Latency")
    process_one_metric("e2el", "E2EL", "End-to-end Latency")

    print("=" * 50)

    return result


async def benchmark_with_warmup(
    backend: str,
    api_url: str,
    base_url: str,
    model_id: str,
    model_name: str,
    tokenizer: PreTrainedTokenizerBase,
    warmup_requests: list[SampleRequest],
    test_requests: list[SampleRequest],
    logprobs: Optional[int],
    request_rate: float,
    burstiness: float,
    disable_tqdm: bool,
    profile: bool,
    selected_percentile_metrics: list[str],
    selected_percentiles: list[float],
    ignore_eos: bool,
    goodput_config_dict: dict[str, float],
    max_concurrency: Optional[int],
    lora_modules: Optional[Iterable[str]],
    extra_body: Optional[dict],
):
    """执行两阶段benchmark：warmup + test"""
    
    print("="*60)
    print("阶段1: Warmup - 填充prefix cache")
    print("="*60)
    
    # Warmup阶段 - 不记录性能指标，重置cache以确保从干净状态开始
    warmup_result = await benchmark(
        backend=backend,
        api_url=api_url,
        base_url=base_url,
        model_id=model_id,
        model_name=model_name,
        tokenizer=tokenizer,
        input_requests=warmup_requests,
        logprobs=logprobs,
        request_rate=request_rate,
        burstiness=burstiness,
        disable_tqdm=disable_tqdm,
        profile=False,  # warmup阶段不需要profile
        selected_percentile_metrics=selected_percentile_metrics,
        selected_percentiles=selected_percentiles,
        ignore_eos=ignore_eos,
        goodput_config_dict=goodput_config_dict,
        max_concurrency=max_concurrency,
        lora_modules=lora_modules,
        extra_body=extra_body,
        reset_cache_before_test=True,  # warmup阶段重置cache
    )
    
    print(f"\nWarmup完成，处理了 {warmup_result['completed']} 个请求")
    print("等待系统稳定...")
    await asyncio.sleep(2)  # 等待2秒让系统稳定
    
    print("\n" + "="*60)
    print("阶段2: Test - 测量prefix cache性能")
    print("="*60)
    
    # Test阶段 - 记录性能指标，不重置cache以利用warmup阶段建立的cache
    test_result = await benchmark(
        backend=backend,
        api_url=api_url,
        base_url=base_url,
        model_id=model_id,
        model_name=model_name,
        tokenizer=tokenizer,
        input_requests=test_requests,
        logprobs=logprobs,
        request_rate=request_rate,
        burstiness=burstiness,
        disable_tqdm=disable_tqdm,
        profile=profile,
        selected_percentile_metrics=selected_percentile_metrics,
        selected_percentiles=selected_percentiles,
        ignore_eos=ignore_eos,
        goodput_config_dict=goodput_config_dict,
        max_concurrency=max_concurrency,
        lora_modules=lora_modules,
        extra_body=extra_body,
        reset_cache_before_test=False,  # test阶段不重置cache，利用warmup建立的cache
    )
    
    # 在测试结果中添加warmup信息
    test_result["warmup_completed"] = warmup_result["completed"]
    test_result["warmup_duration"] = warmup_result["duration"]
    
    return test_result


def check_goodput_args(args):
    # Check and parse goodput arguments
    goodput_config_dict = {}
    VALID_NAMES = ["ttft", "tpot", "e2el"]
    if args.goodput:
        goodput_config_dict = parse_goodput(args.goodput)
        for slo_name, slo_val in goodput_config_dict.items():
            if slo_name not in VALID_NAMES:
                raise ValueError(
                    f"Invalid metric name found, {slo_name}: {slo_val}. "
                    "The service level objective name should be one of "
                    f"{str(VALID_NAMES)}. ")
            if slo_val < 0:
                raise ValueError(
                    f"Invalid value found, {slo_name}: {slo_val}. "
                    "The service level objective value should be "
                    "non-negative.")
    return goodput_config_dict


def parse_goodput(slo_pairs):
    goodput_config_dict = {}
    try:
        for slo_pair in slo_pairs:
            slo_name, slo_val = slo_pair.split(":")
            goodput_config_dict[slo_name] = float(slo_val)
    except ValueError as err:
        raise argparse.ArgumentTypeError(
            "Invalid format found for service level objectives. "
            "Specify service level objectives for goodput as \"KEY:VALUE\" "
            "pairs, where the key is a metric name, and the value is a "
            "number in milliseconds.") from err
    return goodput_config_dict


def save_to_pytorch_benchmark_format(args: argparse.Namespace,
                                     results: dict[str, Any],
                                     file_name: str) -> None:
    metrics = [
        "median_ttft_ms", "mean_ttft_ms", "std_ttft_ms", "p99_ttft_ms",
        "mean_tpot_ms", "median_tpot_ms", "std_tpot_ms", "p99_tpot_ms",
        "median_itl_ms", "mean_itl_ms", "std_itl_ms", "p99_itl_ms"
    ]
    # These raw data might be useful, but they are rather big. They can be added
    # later if needed
    ignored_metrics = ["ttfts", "itls", "generated_texts", "errors"]
    pt_records = convert_to_pytorch_benchmark_format(
        args=args,
        metrics={k: [results[k]]
                 for k in metrics},
        extra_info={
            k: results[k]
            for k in results if k not in metrics and k not in ignored_metrics
        })
    if pt_records:
        # Don't use json suffix here as we don't want CI to pick it up
        pt_file = f"{os.path.splitext(file_name)[0]}.pytorch.json"
        write_to_json(pt_file, pt_records)


def main(args: argparse.Namespace):
    print(args)
    random.seed(args.seed)
    np.random.seed(args.seed)

    backend = args.backend
    model_id = args.model
    model_name = args.served_model_name
    tokenizer_id = args.tokenizer if args.tokenizer is not None else args.model
    tokenizer_mode = args.tokenizer_mode

    if args.base_url is not None:
        api_url = f"{args.base_url}{args.endpoint}"
        base_url = f"{args.base_url}"
    else:
        api_url = f"http://{args.host}:{args.port}{args.endpoint}"
        base_url = f"http://{args.host}:{args.port}"

    tokenizer = get_tokenizer(tokenizer_id,
                              tokenizer_mode=tokenizer_mode,
                              trust_remote_code=args.trust_remote_code)

    if args.dataset_name is None:
        raise ValueError(
            "Please specify '--dataset-name' and the corresponding "
            "'--dataset-path' if required.")

    if args.dataset_name == "sonnet":
        dataset = SonnetDataset(dataset_path=args.dataset_path)
        # For the "sonnet" dataset, formatting depends on the backend.
        if args.backend == "openai-chat":
            input_requests = dataset.sample(num_requests=args.num_prompts,
                                            input_len=args.sonnet_input_len,
                                            output_len=args.sonnet_output_len,
                                            prefix_len=args.sonnet_prefix_len,
                                            tokenizer=tokenizer,
                                            return_prompt_formatted=False)
        else:
            assert getattr(tokenizer, 'chat_template', None) or getattr(tokenizer, 'default_chat_template', None), (
                "Tokenizer/model must have chat template for sonnet dataset.")
            input_requests = dataset.sample(num_requests=args.num_prompts,
                                            input_len=args.sonnet_input_len,
                                            output_len=args.sonnet_output_len,
                                            prefix_len=args.sonnet_prefix_len,
                                            tokenizer=tokenizer,
                                            return_prompt_formatted=True)

    elif args.dataset_name == "hf":
        # all following datasets are implemented from the
        # HuggingFaceDataset base class
        if args.dataset_path in VisionArenaDataset.SUPPORTED_DATASET_PATHS:
            dataset_class = VisionArenaDataset
            args.hf_split = "train"
            args.hf_subset = None
        elif args.dataset_path in InstructCoderDataset.SUPPORTED_DATASET_PATHS:
            dataset_class = InstructCoderDataset
            args.hf_split = "train"
        elif args.dataset_path in ConversationDataset.SUPPORTED_DATASET_PATHS:
            dataset_class = ConversationDataset
        elif args.dataset_path in AIMODataset.SUPPORTED_DATASET_PATHS:
            dataset_class = AIMODataset
            args.hf_split = "train"
        elif args.dataset_path in ASRDataset.SUPPORTED_DATASET_PATHS:
            dataset_class = ASRDataset
            args.hf_split = "train"
        else:
            supported_datasets = set([
                dataset_name for cls in HuggingFaceDataset.__subclasses__()
                for dataset_name in cls.SUPPORTED_DATASET_PATHS
            ])
            raise ValueError(
                f"Unsupported dataset path: {args.dataset_path}. "
                "Huggingface dataset only supports dataset_path"
                f" from one of following: {supported_datasets}. "
                "Please consider contributing if you would "
                "like to add support for additional dataset formats.")

        if (dataset_class.IS_MULTIMODAL and backend not in \
            ["openai-chat", "openai-audio"]):
            # multi-modal benchmark is only available on OpenAI Chat backend.
            raise ValueError(
                "Multi-modal content is only supported on 'openai-chat' and " \
                "'openai-audio' backend.")
        input_requests = dataset_class(
            dataset_path=args.dataset_path,
            dataset_subset=args.hf_subset,
            dataset_split=args.hf_split,
            random_seed=args.seed,
        ).sample(
            num_requests=args.num_prompts,
            tokenizer=tokenizer,  # type: ignore
            output_len=args.hf_output_len,
        )

    elif args.dataset_name == "prefix_cache":
        # 使用两阶段测试：warmup + test
        # 这里只是占位，实际的requests将在benchmark_with_warmup中生成
        input_requests = []

    else:
        # For datasets that follow a similar structure, use a mapping.
        dataset_mapping = {
            "sharegpt":
            lambda: ShareGPTDataset(random_seed=args.seed,
                                    dataset_path=args.dataset_path).sample(
                                        tokenizer=tokenizer,  # type: ignore
                                        num_requests=args.num_prompts,
                                        output_len=args.sharegpt_output_len,
                                    ),
            "burstgpt":
            lambda: BurstGPTDataset(random_seed=args.seed,
                                    dataset_path=args.dataset_path).
            sample(tokenizer=tokenizer, num_requests=args.num_prompts),  # type: ignore
            "random":
            lambda: RandomDataset(dataset_path=args.dataset_path).sample(
                tokenizer=tokenizer,  # type: ignore
                num_requests=args.num_prompts,
                prefix_len=args.random_prefix_len,
                input_len=args.random_input_len,
                output_len=args.random_output_len,
                range_ratio=args.random_range_ratio,
            )
        }

        try:
            input_requests = dataset_mapping[args.dataset_name]()
        except KeyError as err:
            raise ValueError(f"Unknown dataset: {args.dataset_name}") from err
    goodput_config_dict = check_goodput_args(args)

    # Collect the sampling parameters.
    sampling_params = {
        k: v
        for k, v in {
            "top_p": args.top_p,
            "top_k": args.top_k,
            "min_p": args.min_p,
            "temperature": args.temperature
        }.items() if v is not None
    }

    # Sampling parameters are only supported by openai-compatible backend.
    if sampling_params and args.backend not in OPENAI_COMPATIBLE_BACKENDS:
        raise ValueError(
            "Sampling parameters are only supported by openai-compatible "
            "backends.")

    if "temperature" not in sampling_params:
        sampling_params["temperature"] = 0.0  # Default to greedy decoding.

    # Avoid GC processing "static" data - reduce pause times.
    gc.collect()
    gc.freeze()

    if args.dataset_name == "prefix_cache":
        if args.warmup_only:
            # 只运行warmup阶段用于调试
            print("只运行Warmup阶段（调试模式）")
            print(f"Warmup请求数: {args.num_prompts}")
            print(f"Warmup输出token数: 1 (最小化开销，仅填充prefix cache)")
            
            # 生成warmup requests
            warmup_requests = generate_prefix_cache_dataset(
                tokenizer=tokenizer,
                num_requests=args.num_prompts,
                target_hit_rate=0.0,  # Warmup阶段不需要cache hit
                input_len=args.random_input_len,
                output_len=1,  # Warmup阶段只输出1个token，最小化开销
                seed=args.seed,
                mode="warmup"
            )
            
            # 只运行warmup
            benchmark_result = asyncio.run(
                benchmark(
                    backend=backend,
                    api_url=api_url,
                    base_url=base_url,
                    model_id=model_id,
                    model_name=model_name,
                    tokenizer=tokenizer,  # type: ignore
                    input_requests=warmup_requests,
                    logprobs=args.logprobs,
                    request_rate=args.request_rate,
                    burstiness=args.burstiness,
                    disable_tqdm=args.disable_tqdm,
                    profile=args.profile,
                    selected_percentile_metrics=args.percentile_metrics.split(","),
                    selected_percentiles=[
                        float(p) for p in args.metric_percentiles.split(",")
                    ],
                    ignore_eos=args.ignore_eos,
                    goodput_config_dict=goodput_config_dict,
                    max_concurrency=args.max_concurrency,
                    lora_modules=args.lora_modules,
                    extra_body=sampling_params,
                ))
            
        else:
            # 两阶段测试：warmup + test
            print("使用两阶段prefix cache测试")
            
            # 设置warmup和test请求数量
            num_warmup_requests = args.num_prompts
            num_test_requests = args.num_prompts
            
            print(f"Warmup请求数: {num_warmup_requests}")
            print(f"Warmup输出token数: 1 (最小化开销，仅填充prefix cache)")
            print(f"Test请求数: {num_test_requests}")
            print(f"Test输出token数: {args.random_output_len}")
            print(f"总执行请求数: {num_warmup_requests + num_test_requests}")
            print(f"目标cache命中率: {args.target_hit_rate}")
            print(f"测试阶段输出token数: {args.random_output_len}")
            # 生成warmup requests
            warmup_requests = generate_prefix_cache_dataset(
                tokenizer=tokenizer,
                num_requests=num_warmup_requests,
                target_hit_rate=0.0,  # Warmup阶段不需要cache hit
                input_len=args.random_input_len,
                output_len=1,  # Warmup阶段只输出1个token，最小化开销
                seed=args.seed,
                mode="warmup"
            )
            
            # 生成test requests（基于warmup requests）
            test_requests = generate_prefix_cache_dataset(
                tokenizer=tokenizer,
                num_requests=num_test_requests,
                target_hit_rate=args.target_hit_rate,
                input_len=args.random_input_len,
                output_len=args.random_output_len,
                seed=args.seed,
                warmup_requests=warmup_requests,
                mode="test"
            )
            
            benchmark_result = asyncio.run(
                benchmark_with_warmup(
                    backend=backend,
                    api_url=api_url,
                    base_url=base_url,
                    model_id=model_id,
                    model_name=model_name,
                    tokenizer=tokenizer,  # type: ignore
                    warmup_requests=warmup_requests,
                    test_requests=test_requests,
                    logprobs=args.logprobs,
                    request_rate=args.request_rate,
                    burstiness=args.burstiness,
                    disable_tqdm=args.disable_tqdm,
                    profile=args.profile,
                    selected_percentile_metrics=args.percentile_metrics.split(","),
                    selected_percentiles=[
                        float(p) for p in args.metric_percentiles.split(",")
                    ],
                    ignore_eos=args.ignore_eos,
                    goodput_config_dict=goodput_config_dict,
                    max_concurrency=args.max_concurrency,
                    lora_modules=args.lora_modules,
                    extra_body=sampling_params,
                ))
    else:
        # 传统的单阶段测试
        benchmark_result = asyncio.run(
            benchmark(
                backend=backend,
                api_url=api_url,
                base_url=base_url,
                model_id=model_id,
                model_name=model_name,
                tokenizer=tokenizer,  # type: ignore
                input_requests=input_requests,
                logprobs=args.logprobs,
                request_rate=args.request_rate,
                burstiness=args.burstiness,
                disable_tqdm=args.disable_tqdm,
                profile=args.profile,
                selected_percentile_metrics=args.percentile_metrics.split(","),
                selected_percentiles=[
                    float(p) for p in args.metric_percentiles.split(",")
                ],
                ignore_eos=args.ignore_eos,
                goodput_config_dict=goodput_config_dict,
                max_concurrency=args.max_concurrency,
                lora_modules=args.lora_modules,
                extra_body=sampling_params,
            ))

    # Save config and results to json
    if args.save_result or args.append_result:
        result_json: dict[str, Any] = {}

        # Setup
        current_dt = datetime.now().strftime("%Y%m%d-%H%M%S")
        result_json["date"] = current_dt
        result_json["backend"] = backend
        result_json["model_id"] = model_id
        result_json["tokenizer_id"] = tokenizer_id
        result_json["num_prompts"] = args.num_prompts

        # Metadata
        if args.metadata:
            for item in args.metadata:
                if "=" in item:
                    kvstring = item.split("=")
                    result_json[kvstring[0].strip()] = kvstring[1].strip()
                else:
                    raise ValueError(
                        "Invalid metadata format. Please use KEY=VALUE format."
                    )
        # Traffic
        result_json["request_rate"] = (args.request_rate if args.request_rate
                                       < float("inf") else "inf")
        result_json["burstiness"] = args.burstiness
        result_json["max_concurrency"] = args.max_concurrency

        # Merge with benchmark result
        result_json = {**result_json, **benchmark_result}

        if not args.save_detailed:
            # Remove fields with too many data points
            for field in [
                    "input_lens", "output_lens", "ttfts", "itls",
                    "generated_texts", "errors"
            ]:
                if field in result_json:
                    del result_json[field]

        # Save to file
        base_model_id = model_id.split("/")[-1]
        max_concurrency_str = (f"-concurrency{args.max_concurrency}"
                               if args.max_concurrency is not None else "")
        file_name = f"{backend}-{args.request_rate}qps{max_concurrency_str}-{base_model_id}-{current_dt}.json"  #noqa
        if args.result_filename:
            file_name = args.result_filename
        if args.result_dir:
            file_name = os.path.join(args.result_dir, file_name)
        with open(file_name,
                  mode="a+" if args.append_result else "w",
                  encoding='utf-8') as outfile:
            # Append a newline.
            if args.append_result and outfile.tell() != 0:
                outfile.write("\n")
            json.dump(result_json, outfile)
        save_to_pytorch_benchmark_format(args, result_json, file_name)


if __name__ == "__main__":
    parser = FlexibleArgumentParser(
        description="Benchmark the online serving throughput.")
    parser.add_argument(
        "--backend",
        type=str,
        default="vllm",
        choices=list(ASYNC_REQUEST_FUNCS.keys()),
    )
    parser.add_argument(
        "--base-url",
        type=str,
        default=None,
        help="Server or API base url if not using http host and port.",
    )
    # Use 127.0.0.1 here instead of localhost to force the use of ipv4
    parser.add_argument("--host", type=str, default="127.0.0.1")
    parser.add_argument("--port", type=int, default=8000)
    parser.add_argument(
        "--endpoint",
        type=str,
        default="/v1/completions",
        help="API endpoint.",
    )
    parser.add_argument(
        "--dataset-name",
        type=str,
        default="sharegpt",
        choices=["sharegpt", "burstgpt", "sonnet", "random", "hf", "prefix_cache"],
        help="Name of the dataset to benchmark on.",
    )
    parser.add_argument("--dataset-path",
                        type=str,
                        default=None,
                        help="Path to the sharegpt/sonnet dataset. "
                        "Or the huggingface dataset ID if using HF dataset.")
    parser.add_argument(
        "--max-concurrency",
        type=int,
        default=None,
        help="Maximum number of concurrent requests. This can be used "
        "to help simulate an environment where a higher level component "
        "is enforcing a maximum number of concurrent requests. While the "
        "--request-rate argument controls the rate at which requests are "
        "initiated, this argument will control how many are actually allowed "
        "to execute at a time. This means that when used in combination, the "
        "actual request rate may be lower than specified with --request-rate, "
        "if the server is not processing requests fast enough to keep up.")

    parser.add_argument(
        "--model",
        type=str,
        required=True,
        help="Name of the model.",
    )
    parser.add_argument(
        "--tokenizer",
        type=str,
        help=
        "Name or path of the tokenizer, if not using the default tokenizer.",  # noqa: E501
    )
    parser.add_argument("--use-beam-search", action="store_true")
    parser.add_argument(
        "--num-prompts",
        type=int,
        default=1000,
        help="Number of prompts to process.",
    )
    parser.add_argument(
        "--logprobs",
        type=int,
        default=None,
        help=("Number of logprobs-per-token to compute & return as part of "
              "the request. If unspecified, then either (1) if beam search "
              "is disabled, no logprobs are computed & a single dummy "
              "logprob is returned for each token; or (2) if beam search "
              "is enabled 1 logprob per token is computed"),
    )
    parser.add_argument(
        "--request-rate",
        type=float,
        default=float("inf"),
        help="Number of requests per second. If this is inf, "
        "then all the requests are sent at time 0. "
        "Otherwise, we use Poisson process or gamma distribution "
        "to synthesize the request arrival times.",
    )
    parser.add_argument(
        "--burstiness",
        type=float,
        default=1.0,
        help="Burstiness factor of the request generation. "
        "Only take effect when request_rate is not inf. "
        "Default value is 1, which follows Poisson process. "
        "Otherwise, the request intervals follow a gamma distribution. "
        "A lower burstiness value (0 < burstiness < 1) results in more "
        "bursty requests. A higher burstiness value (burstiness > 1) "
        "results in a more uniform arrival of requests.",
    )
    parser.add_argument("--seed", type=int, default=0)
    parser.add_argument(
        "--trust-remote-code",
        action="store_true",
        help="Trust remote code from huggingface",
    )
    parser.add_argument(
        "--disable-tqdm",
        action="store_true",
        help="Specify to disable tqdm progress bar.",
    )
    parser.add_argument(
        "--profile",
        action="store_true",
        help="Use Torch Profiler. The endpoint must be launched with "
        "VLLM_TORCH_PROFILER_DIR to enable profiler.",
    )
    parser.add_argument(
        "--save-result",
        action="store_true",
        help="Specify to save benchmark results to a json file",
    )
    parser.add_argument(
        "--save-detailed",
        action="store_true",
        help="When saving the results, whether to include per request "
        "information such as response, error, ttfs, tpots, etc.",
    )
    parser.add_argument(
        "--append-result",
        action="store_true",
        help="Append the benchmark result to the existing json file.",
    )
    parser.add_argument(
        "--metadata",
        metavar="KEY=VALUE",
        nargs="*",
        help="Key-value pairs (e.g, --metadata version=0.3.3 tp=1) "
        "for metadata of this run to be saved in the result JSON file "
        "for record keeping purposes.",
    )
    parser.add_argument(
        "--result-dir",
        type=str,
        default=None,
        help="Specify directory to save benchmark json results."
        "If not specified, results are saved in the current directory.",
    )
    parser.add_argument(
        "--result-filename",
        type=str,
        default=None,
        help="Specify the filename to save benchmark json results."
        "If not specified, results will be saved in "
        "{backend}-{args.request_rate}qps-{base_model_id}-{current_dt}.json"
        " format.",
    )
    parser.add_argument(
        "--ignore-eos",
        action="store_true",
        help="Set ignore_eos flag when sending the benchmark request."
        "Warning: ignore_eos is not supported in deepspeed_mii and tgi.")
    parser.add_argument(
        "--percentile-metrics",
        type=str,
        default="ttft,tpot,itl",
        help="Comma-separated list of selected metrics to report percentils. "
        "This argument specifies the metrics to report percentiles. "
        "Allowed metric names are \"ttft\", \"tpot\", \"itl\", \"e2el\". "
        "Default value is \"ttft,tpot,itl\".")
    parser.add_argument(
        "--metric-percentiles",
        type=str,
        default="99",
        help="Comma-separated list of percentiles for selected metrics. "
        "To report 25-th, 50-th, and 75-th percentiles, use \"25,50,75\". "
        "Default value is \"99\". "
        "Use \"--percentile-metrics\" to select metrics.",
    )
    parser.add_argument(
        "--goodput",
        nargs="+",
        required=False,
        help="Specify service level objectives for goodput as \"KEY:VALUE\" "
        "pairs, where the key is a metric name, and the value is in "
        "milliseconds. Multiple \"KEY:VALUE\" pairs can be provided, "
        "separated by spaces. Allowed request level metric names are "
        "\"ttft\", \"tpot\", \"e2el\". For more context on the definition of "
        "goodput, refer to DistServe paper: https://arxiv.org/pdf/2401.09670 "
        "and the blog: https://hao-ai-lab.github.io/blogs/distserve")

    # group for dataset specific arguments
    sonnet_group = parser.add_argument_group("sonnet dataset options")
    sonnet_group.add_argument(
        "--sonnet-input-len",
        type=int,
        default=550,
        help=
        "Number of input tokens per request, used only for sonnet dataset.",
    )
    sonnet_group.add_argument(
        "--sonnet-output-len",
        type=int,
        default=150,
        help=
        "Number of output tokens per request, used only for sonnet dataset.",
    )
    sonnet_group.add_argument(
        "--sonnet-prefix-len",
        type=int,
        default=200,
        help=
        "Number of prefix tokens per request, used only for sonnet dataset.",
    )

    sharegpt_group = parser.add_argument_group("sharegpt dataset options")
    sharegpt_group.add_argument(
        "--sharegpt-output-len",
        type=int,
        default=None,
        help="Output length for each request. Overrides the output length "
        "from the ShareGPT dataset.")

    random_group = parser.add_argument_group("random dataset options")
    random_group.add_argument(
        "--random-input-len",
        type=int,
        default=1024,
        help=
        "Number of input tokens per request, used only for random sampling.",
    )
    random_group.add_argument(
        "--random-output-len",
        type=int,
        default=128,
        help=
        "Number of output tokens per request, used only for random sampling.",
    )
    random_group.add_argument(
        "--random-range-ratio",
        type=float,
        default=0.0,
        help="Range ratio for sampling input/output length, "
        "used only for random sampling. Must be in the range [0, 1) to define "
        "a symmetric sampling range"
        "[length * (1 - range_ratio), length * (1 + range_ratio)].",
    )
    random_group.add_argument(
        "--random-prefix-len",
        type=int,
        default=0,
        help=("Number of fixed prefix tokens before the random context "
              "in a request. "
              "The total input length is the sum of `random-prefix-len` and "
              "a random "
              "context length sampled from [input_len * (1 - range_ratio), "
              "input_len * (1 + range_ratio)]."),
    )

    hf_group = parser.add_argument_group("hf dataset options")
    hf_group.add_argument("--hf-subset",
                          type=str,
                          default=None,
                          help="Subset of the HF dataset.")
    hf_group.add_argument("--hf-split",
                          type=str,
                          default=None,
                          help="Split of the HF dataset.")
    hf_group.add_argument(
        "--hf-output-len",
        type=int,
        default=None,
        help="Output length for each request. Overrides the output lengths "
        "from the sampled HF dataset.",
    )

    sampling_group = parser.add_argument_group("sampling parameters")
    sampling_group.add_argument(
        "--top-p",
        type=float,
        default=None,
        help="Top-p sampling parameter. Only has effect on openai-compatible "
        "backends.")
    sampling_group.add_argument(
        "--top-k",
        type=int,
        default=None,
        help="Top-k sampling parameter. Only has effect on openai-compatible "
        "backends.")
    sampling_group.add_argument(
        "--min-p",
        type=float,
        default=None,
        help="Min-p sampling parameter. Only has effect on openai-compatible "
        "backends.")
    sampling_group.add_argument(
        "--temperature",
        type=float,
        default=None,
        help="Temperature sampling parameter. Only has effect on "
        "openai-compatible backends. If not specified, default to greedy "
        "decoding (i.e. temperature==0.0).")

    parser.add_argument(
        '--tokenizer-mode',
        type=str,
        default="auto",
        choices=['auto', 'slow', 'mistral', 'custom'],
        help='The tokenizer mode.\n\n* "auto" will use the '
        'fast tokenizer if available.\n* "slow" will '
        'always use the slow tokenizer. \n* '
        '"mistral" will always use the `mistral_common` tokenizer. \n*'
        '"custom" will use --tokenizer to select the preregistered tokenizer.')

    parser.add_argument("--served-model-name",
                        type=str,
                        default=None,
                        help="The model name used in the API. "
                        "If not specified, the model name will be the "
                        "same as the ``--model`` argument. ")

    parser.add_argument("--lora-modules",
                        nargs='+',
                        default=None,
                        help="A subset of LoRA module names passed in when "
                        "launching the server. For each request, the "
                        "script chooses a LoRA module at random.")

    # Prefix cache control options
    parser.add_argument(
        "--target-hit-rate",
        type=float,
        default=0.5,
        help="Target prefix cache hit rate (0.0-1.0), used with prefix_cache dataset. Default=0.5"
    )
    parser.add_argument(
        "--warmup-only",
        action="store_true",
        help="Only run warmup phase for debugging, skip test phase. Used with prefix_cache dataset."
    )


    args = parser.parse_args()

    main(args)
