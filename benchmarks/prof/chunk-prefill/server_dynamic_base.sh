#!/bin/bash
# 防止重复运行的锁文件机制
LOCKFILE="/tmp/prof_llama_test.lock"
if [ -f "$LOCKFILE" ]; then
    echo "脚本已在运行中 (锁文件: $LOCKFILE)，退出..."
    exit 1
fi
echo $$ > "$LOCKFILE"
# 清理函数
cleanup() {
    echo "清理锁文件..."
    rm -f "$LOCKFILE"
    exit
}
trap cleanup EXIT INT TERM
nohup bash -c 'HIP_VISIBLE_DEVICES=0,1 PYTHONPATH=/home/<USER>/vllm/ VLLM_USE_FLASH_ATTN_PA=1 VLLM_TORCH_PROFILER_DIR=/home/<USER>/vllm/benchmarks/prof/qwen3-30B/ python -m vllm.entrypoints.openai.api_server --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port 8061 --disable-log-requests --no-enable-chunked-prefill --no-enable-prefix-caching ' >/home/<USER>/benchmarks/prof/chunk-prefill/Qwen3-30B-A3B_server_base.log 2>&1 &
SERVER1_PID=$!
nohup bash -c 'HIP_VISIBLE_DEVICES=2,3 PYTHONPATH=/home/<USER>/vllm/ VLLM_USE_FLASH_ATTN_PA=1 VLLM_TORCH_PROFILER_DIR=/home/<USER>/vllm/benchmarks/prof/qwen3-30B/ python -m vllm.entrypoints.openai.api_server --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port 8062 --disable-log-requests --enable-chunked-prefill --no-enable-prefix-caching ' >/home/<USER>/benchmarks/prof/chunk-prefill/Qwen3-30B-A3B_server_chunk.log 2>&1 &
SERVER2_PID=$!
# 等待服务就绪
while ! curl -s --head http://localhost:8061/v1/models > /dev/null; do sleep 2; done
while ! curl -s --head http://localhost:8062/v1/models > /dev/null; do sleep 2; done
# --- Test Cases ---
QWEN_CASES=(
    "1000 48"
    # "2000 33"
    # "3600 27"
    # "20000 8"
    # "200 67"
)
QWEN_CASES_CHUNK=(
    "1000 48"
    # "2000 33"
    # "3600 27"
    # "20000 8"
    # "200 67"
)
# 并行运行测试
(
    PROF_DIR="/home/<USER>/benchmarks/prof/chunk-prefill/qwen3-30B-base"
    LOG_FILE="/home/<USER>/benchmarks/prof/chunk-prefill/qwen3-30B-base/Qwen3-30B-A3B_base.log"
   

    for case in "${QWEN_CASES[@]}"; do
        read -r input_len num_prompts <<<"$case"

        # 标注分隔行到日志
        echo "${num_prompts}-${input_len}" >> "${LOG_FILE}"

        # 执行并将输出追加到日志
        HIP_VISIBLE_DEVICES=0,1 python3 /home/<USER>/vllm/benchmarks/benchmark_serving.py \
            --model qwen \
            --tokenizer /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ \
            --random-input-len "${input_len}" \
            --random-output-len 10 \
            --num-prompts "${num_prompts}" \
            --max-concurrency "${num_prompts}" \
            --ignore-eos \
            --dataset-name random \
            --port 8061 \
            --profile \
            >> "${LOG_FILE}" 2>&1
        sleep 60
        OUT_DIR="${PROF_DIR}/${num_prompts}_${input_len}"
        while true; do
            count=$(ls ${PROF_DIR}/*.gz 2>/dev/null | wc -l)
            if [ "$count" -ge 2 ]; then
                break
            fi
            sleep 2
        done
        sleep 10
        mkdir -p "${OUT_DIR}"
        mv "${PROF_DIR}"/*.gz "${OUT_DIR}"/
    done
) &
# TEST1_PID=$!
(
    PROF_DIR="/home/<USER>/benchmarks/prof/chunk-prefill/qwen3-30B-chunk"
    LOG_FILE="/home/<USER>/benchmarks/prof/chunk-prefill/qwen3-30B-chunk/Qwen3-30B-A3B_chunk.log"
    for case in "${QWEN_CASES_CHUNK[@]}"; do
        read -r input_len num_prompts <<<"$case"
        echo "开始测试用例: ${num_prompts} prompts, ${input_len} tokens - $(date)"
        # 终止可能存在的其他 benchmark 进程，避免重复运行
        pkill -f "benchmark_serving.py" 2>/dev/null || true
        sleep 5
        # 清理旧的 profiler 文件，避免计数错误
        rm -f ${PROF_DIR}/*.gz 2>/dev/null || true
        echo "清理完成，当前文件数: $(ls ${PROF_DIR}/*.gz 2>/dev/null | wc -l)"
        # 标注分隔行到日志
        echo "${num_prompts}-${input_len}" >> "${LOG_FILE}"
        HIP_VISIBLE_DEVICES=0,1 python3 /home/<USER>/vllm/benchmarks/benchmark_serving.py \
            --model qwen \
            --tokenizer /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ \
            --random-input-len "${input_len}" \
            --random-output-len 500 \
            --num-prompts ${num_prompts} \
            --max-concurrency ${num_prompts} \
            --ignore-eos \
            --dataset-name random \
            --port 8062 \
            --profile \
            >> "${LOG_FILE}" 2>&1
        echo "benchmark完成，等待profiler文件生成..."
        sleep 30
        OUT_DIR="${PROF_DIR}/${num_prompts}_${input_len}"
        timeout=180  # 3分钟超时
        elapsed=0
        while true; do
            count=$(ls ${PROF_DIR}/*.gz 2>/dev/null | wc -l)
            echo "$(date): 找到 ${count}/4 个profiler文件"
            if [ "$count" -ge 4 ]; then
                echo "找到足够文件，退出等待循环"
                break
            fi
            if [ $elapsed -ge $timeout ]; then
                echo "超时退出: 等待${timeout}秒后仍只有${count}个文件"
                ls -la ${PROF_DIR}/
                break
            fi
            sleep 10
            elapsed=$((elapsed + 10))
        done
        # 移动文件
        if [ "$count" -ge 4 ]; then
            mkdir -p "${OUT_DIR}"
            # 检查文件是否存在，避免mv错误
            if ls ${PROF_DIR}/*.gz >/dev/null 2>&1; then
                mv ${PROF_DIR}/*.gz "${OUT_DIR}"/
                echo "文件移动完成: $(ls ${OUT_DIR}/*.gz | wc -l) 个文件到 ${OUT_DIR}"
            else
                echo "警告: 虽然计数为${count}，但没找到.gz文件"
            fi
        else
            echo "跳过移动: 文件数量不足(${count}/4)"
        fi
        echo "测试用例 ${num_prompts}-${input_len} 完成"
        echo "----------------------------------------"
    done
) &
TEST2_PID=$!
# 等待测试全部完成
# wait $TEST1_PID
wait $TEST2_PID
# 停止服务
# kill $SERVER1_PID
kill $SERVER2_PID