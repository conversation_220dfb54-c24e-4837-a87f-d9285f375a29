# Common dependencies
-r common.txt

numba == 0.60.0; python_version == '3.9' # v0.61 doesn't support Python 3.9. Required for N-gram speculative decoding
numba == 0.61.2; python_version > '3.9'

# Dependencies for hcus
boto3
botocore
datasets
ray>=2.10.0,<2.45.0
peft
pytest-asyncio
tensorizer>=2.9.0
packaging>=24.2
setuptools>=77.0.3,<80.0.0
setuptools-scm>=8
runai-model-streamer==0.11.0
runai-model-streamer-s3==0.11.0
setuptools_scm>=8
cmake==3.29

torch == 2.5.1
triton == 3.0.0
flash_attn == 2.6.1
flash_mla == 1.0.0
lmslim == 0.3.0
numa
python-multipart
pytrie

